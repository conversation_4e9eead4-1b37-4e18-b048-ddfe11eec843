<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User & Transaction Graph Visualization</title>

    <!-- Cytoscape.js -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/cytoscape/3.23.0/cytoscape.min.js"></script>

    <!-- Popper.js and Tippy.js (required for tooltips) -->
    <script src="https://unpkg.com/@popperjs/core@2"></script>
    <script src="https://unpkg.com/tippy.js@6"></script>

    <!-- Cytoscape extensions -->
    <script>
        // Define global variables for extensions
        var cytoscapeCoseBilkent, cytoscapePopper, cytoscapeNavigator;
    </script>
    <script src="https://cdn.jsdelivr.net/npm/cytoscape-cose-bilkent@4.1.0/cytoscape-cose-bilkent.min.js"></script>
    <script>
        // Store the extension in a global variable
        if (typeof cytoscape !== 'undefined' && typeof window.cytoscapeCoseBilkent === 'undefined') {
            cytoscapeCoseBilkent = window.cytoscapeCoseBilkent;
        }
    </script>

    <script src="https://cdn.jsdelivr.net/npm/cytoscape-navigator@2.0.1/cytoscape-navigator.min.js"></script>
    <script>
        // Store the extension in a global variable
        if (typeof cytoscape !== 'undefined' && typeof window.cytoscapeNavigator === 'undefined') {
            cytoscapeNavigator = window.cytoscapeNavigator;
        }
    </script>

    <script src="https://cdn.jsdelivr.net/npm/cytoscape-popper@2.0.0/cytoscape-popper.min.js"></script>
    <script>
        // Store the extension in a global variable
        if (typeof cytoscape !== 'undefined' && typeof window.cytoscapePopper === 'undefined') {
            cytoscapePopper = window.cytoscapePopper;
        }
    </script>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="/static/css/style.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 sidebar">
                <h1 class="mt-3 mb-4">Graph Explorer</h1>

                <!-- Search and Filter Section -->
                <div class="card mb-3">
                    <div class="card-header">
                        <h5>Search & Filter</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="searchInput" class="form-label">Search</label>
                            <input type="text" class="form-control" id="searchInput" placeholder="Search by name, ID...">
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Node Types</label>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="showUsers" checked>
                                <label class="form-check-label" for="showUsers">Users</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="showTransactions" checked>
                                <label class="form-check-label" for="showTransactions">Transactions</label>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Relationship Types</label>
                            <div id="relationshipFilters">
                                <!-- Relationship filters will be added dynamically -->
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Users List -->
                <div class="card mb-3">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5>Users</h5>
                        <button class="btn btn-sm btn-outline-secondary" id="collapseUsersBtn">
                            <i class="fas fa-chevron-down"></i>
                        </button>
                    </div>
                    <div class="card-body" id="usersListContainer">
                        <div class="input-group mb-3">
                            <input type="text" class="form-control" id="userSearchInput" placeholder="Filter users...">
                            <button class="btn btn-outline-secondary" type="button" id="clearUserSearch">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                        <div class="list-group" id="usersList">
                            <!-- Users will be added dynamically -->
                        </div>
                    </div>
                </div>

                <!-- Transactions List -->
                <div class="card mb-3">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5>Transactions</h5>
                        <button class="btn btn-sm btn-outline-secondary" id="collapseTransactionsBtn">
                            <i class="fas fa-chevron-down"></i>
                        </button>
                    </div>
                    <div class="card-body" id="transactionsListContainer">
                        <div class="input-group mb-3">
                            <input type="text" class="form-control" id="transactionSearchInput" placeholder="Filter transactions...">
                            <button class="btn btn-outline-secondary" type="button" id="clearTransactionSearch">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                        <div class="list-group" id="transactionsList">
                            <!-- Transactions will be added dynamically -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- Main Graph Area -->
            <div class="col-md-9 graph-container">
                <div class="toolbar mb-2">
                    <button class="btn btn-primary" id="resetViewBtn">
                        <i class="fas fa-home"></i> Reset View
                    </button>
                    <button class="btn btn-outline-secondary" id="toggleLayoutBtn">
                        <i class="fas fa-project-diagram"></i> Change Layout
                    </button>
                    <button class="btn btn-outline-secondary" id="exportImageBtn">
                        <i class="fas fa-download"></i> Export Image
                    </button>
                    <div class="btn-group" role="group">
                        <button class="btn btn-outline-secondary" id="zoomInBtn">
                            <i class="fas fa-search-plus"></i>
                        </button>
                        <button class="btn btn-outline-secondary" id="zoomOutBtn">
                            <i class="fas fa-search-minus"></i>
                        </button>
                    </div>
                </div>

                <!-- Graph Canvas -->
                <div id="cy"></div>

                <!-- Node Details Panel -->
                <div class="card node-details" id="nodeDetails">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 id="nodeDetailsTitle">Node Details</h5>
                        <button class="btn btn-sm btn-outline-secondary" id="closeNodeDetails">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="card-body" id="nodeDetailsContent">
                        <p class="text-muted">Select a node to view details</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Custom JS -->
    <script src="/static/js/graph.js"></script>
</body>
</html>
